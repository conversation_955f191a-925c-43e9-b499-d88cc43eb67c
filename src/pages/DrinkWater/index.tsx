import React, { useEffect, useContext } from "react";
import { View, ScrollView, Platform, NativeModules, Image, Text, TouchableOpacity, BackHandler, PanResponder, Dimensions } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList, RootNavigationProps } from "router/type";
import { usePageReport } from "hooks/usePageReport";
import { getStyles } from "./style";
import Header from "components/DrinkWater/Header";
import EightGlasses from "components/DrinkWater/EightGlasses";
import WaterTip from "components/DrinkWater/WaterTip";
import WaterWeekCard from "components/DrinkWater/WaterWeek";
import WaterFullWeekCard from "components/DrinkWater/WaterFullWeek";
import { PageEventEmitter } from "defs";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import { useAtomValue } from "jotai";
import { waterInfoAtom } from "./store";
import { Page } from "@xmly/rn-sdk";
import getUrlToOpen from "utilsV2/getUrlToOpen";
import xmlog from '../../utilsV2/xmlog'
import getXMRequestId from 'utilsV2/getXMRequestId';
import { NativeInfoContext } from "contextV2/nativeInfoContext";
import navigationService from "../../services/navigationService";

const headBg = 'https://imagev2.xmcdn.com/storages/7aba-audiofreehighqps/93/48/GKwRIJIMOXqeAAJdqwPWsMgy.png'

export default function DrinkWater(props: StackScreenProps<RootStackParamList>) {
  const styles = getStyles();
  const navigation = useNavigation<RootNavigationProps>();
  const nativeInfo = useContext(NativeInfoContext);

  const waterInfo = useAtomValue(waterInfoAtom);
  const [xmRequestId, setXmRequestId] = React.useState('');

  // 屏幕宽度
  const screenWidth = Dimensions.get('window').width;

  // 创建PanResponder来处理左滑手势
  const panResponder = React.useMemo(() => {
    if (Platform.OS !== 'ios' || nativeInfo?.route !== 'drinkWater') {
      return null;
    }

    return PanResponder.create({
      onStartShouldSetPanResponder: (evt, gestureState) => {
        // 只在屏幕左边缘开始滑动时响应
        return evt.nativeEvent.pageX < 20;
      },
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // 检查是否是从左边缘开始的右滑手势
        return evt.nativeEvent.pageX < 20 && gestureState.dx > 10 && Math.abs(gestureState.dy) < 50;
      },
      onPanResponderGrant: () => {
        console.log('DrinkWater: Custom swipe gesture started');
      },
      onPanResponderMove: (evt, gestureState) => {
        // 可以在这里添加视觉反馈
      },
      onPanResponderRelease: (evt, gestureState) => {
        console.log('DrinkWater: Custom swipe gesture released, dx:', gestureState.dx);
        // 如果滑动距离超过屏幕宽度的1/3，触发返回
        if (gestureState.dx > screenWidth / 3) {
          console.log('DrinkWater: Custom swipe gesture triggered back navigation');
          navigationService.goBackFromDrinkWater();
        }
      },
    });
  }, [nativeInfo?.route, screenWidth]);

  usePageReport({
    pageViewCode: 69117, 
    pageExitCode: 69118,
    currPage: 'drinkWaterActivityPage',
    otherProps: props
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    // 如果是通过 route=drinkWater 进入的，立即禁用原生滑动手势
    if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
      console.log('DrinkWater: Immediately disabling native swipe gesture for route=drinkWater');
      NativeModules.CompatibleIOS.setSwipBackGestureEnable(false);
    }

    // 自定义返回处理函数
    const handleCustomBack = () => {
      console.log('DrinkWater: handleCustomBack called natigation.canGoBack', navigation.canGoBack());
      try {
        // 如果是通过 route=drinkWater 进入的，直接使用自定义返回逻辑
        if (nativeInfo?.route === 'drinkWater') {
          console.log('DrinkWater: Using custom back logic for route=drinkWater');
          navigationService.goBackFromDrinkWater();
        } else if (navigation.canGoBack()) {
          console.log('DrinkWater: Using default back logic');
          navigation.goBack();
        } else {
          navigationService.goBackFromDrinkWater();
        }
      } catch (error) {
        console.error('DrinkWater: Error in handleCustomBack:', error);
      }
    };

    // Android硬件返回键处理
    const handleAndroidBackPress = () => {
      console.log('DrinkWater: Android back press, route:', nativeInfo?.route);
      // 如果是通过 route=drinkWater 进入的，使用自定义返回逻辑
      if (nativeInfo?.route === 'drinkWater') {
        handleCustomBack();
        return true; // 阻止默认返回行为
      }
      console.log('DrinkWater: Using default back behavior');
      return false; // 允许默认返回行为
    };

    // iOS手势返回处理 - 使用beforeRemove事件
    const handleIosBeforeRemove = (e: any) => {
      console.log('DrinkWater: iOS beforeRemove triggered, route:', nativeInfo?.route, 'action:', e.data?.action?.type);
      if (nativeInfo?.route === 'drinkWater') {
        // 检查是否是手势返回（而不是程序化导航）
        // 如果导航服务正在处理，则不拦截
        if (navigationService.getIsNavigating()) {
          console.log('DrinkWater: Navigation service is handling, allowing default behavior');
          return;
        }
        console.log('DrinkWater: Preventing default navigation and using custom back logic');
        // 阻止默认行为
        e.preventDefault();
        // 执行自定义返回逻辑
        handleCustomBack();
      }
    };

    // 根据平台注册不同的事件监听器
    const backHandler = Platform.OS === 'android'
      ? BackHandler.addEventListener('hardwareBackPress', handleAndroidBackPress)
      : null;

    const beforeRemoveListener = Platform.OS === 'ios'
      ? navigation.addListener('beforeRemove', handleIosBeforeRemove)
      : null;

    const blurListener = navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    const focusListener = navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        // 如果是通过 route=drinkWater 进入的，禁用原生滑动手势，让React Navigation处理
        if (nativeInfo?.route === 'drinkWater') {
          console.log('DrinkWater: Disabling native swipe gesture for route=drinkWater');
          NativeModules.CompatibleIOS.setSwipBackGestureEnable(false);
        } else {
          NativeModules.CompatibleIOS.setSwipBackGestureEnable(true);
        }
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      console.log("DrinkWater onResuming....................")
      // 可以在这里刷新数据
    });

    return () => {
      // 清理事件监听器
      backHandler?.remove();
      beforeRemoveListener?.();
      blurListener?.();
      focusListener?.();
      resumeListener?.remove();

      // 组件卸载时恢复原生手势设置
      if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
        console.log('DrinkWater: Restoring native swipe gesture on unmount');
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true);
      }
    }
  }, [navigation, nativeInfo?.route]);

  // 额外的useEffect来确保原生手势被正确禁用
  useEffect(() => {
    if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
      // 延迟一点时间确保页面完全加载后再禁用手势
      const timer = setTimeout(() => {
        console.log('DrinkWater: Double-checking native swipe gesture is disabled');
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false);
      }, 100);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [nativeInfo?.route]);

  useEffect(() => {
    (async () => {
      const reqId = await getXMRequestId();
      setXmRequestId(reqId);
    })();
  }, []);

  function handleRulePress() {
    if (waterInfo?.rulePage) {
      xmlog.click(69119, undefined, {
        currPage: 'drinkWaterActivityPage',
        Item: '规则',
        xmRequestId,
      })
      Page.start(getUrlToOpen(waterInfo.rulePage));
    }
  }

  return (
    <View style={styles.container} {...(panResponder?.panHandlers || {})}>
      <Header />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <Image
          source={{ uri: headBg }}
          style={styles.backgroundImage}
        />
        {waterInfo?.rulePage? (
          <TouchableOpacity
            style={styles.ruleButton}
            onPress={handleRulePress}
            activeOpacity={0.8}
          >
            <Text style={styles.ruleText}>规则</Text>
          </TouchableOpacity>
        ) : null}
        <View style={styles.contentWrapper}>
          <EightGlasses xmRequestId={xmRequestId} />
          <WaterTip />
          {waterInfo?.waterWeek && (
            <WaterWeekCard
              waterWeek={waterInfo.waterWeek}
              xmRequestId={xmRequestId}
            />
          )}
          {waterInfo?.waterFullWeek && (
            <WaterFullWeekCard
              waterFullWeek={waterInfo.waterFullWeek}
              xmRequestId={xmRequestId}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}
