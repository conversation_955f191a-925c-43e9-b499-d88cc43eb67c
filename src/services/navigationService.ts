import { NavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from '../router/type';

class NavigationService {
  private navigationRef: React.RefObject<NavigationContainerRef> | null = null;
  private originalInitialRouteName: keyof RootStackParamList = 'Home';
  private isNavigating: boolean = false; // 防止重复导航

  setNavigationRef(ref: React.RefObject<NavigationContainerRef>) {
    this.navigationRef = ref;
  }

  setOriginalInitialRouteName(routeName: keyof RootStackParamList) {
    this.originalInitialRouteName = routeName;
  }

  // 检查是否正在导航
  getIsNavigating() {
    return this.isNavigating;
  }

  // 自定义的从喝水页面返回逻辑
  goBackFromDrinkWater() {
    // 防止重复调用
    if (this.isNavigating) {
      console.log('goBackFromDrinkWater: Already navigating, skipping');
      return;
    }

    console.log('goBackFromDrinkWater called, navigating to:', this.originalInitialRouteName);
    if (this.navigationRef?.current) {
      try {
        this.isNavigating = true;
        this.navigationRef.current.reset({
          index: 0,
          routes: [{ name: this.originalInitialRouteName }],
        });
        // 重置导航状态
        setTimeout(() => {
          this.isNavigating = false;
        }, 1000);
      } catch (error) {
        console.error('Error in goBackFromDrinkWater:', error);
        this.isNavigating = false;
      }
    }
  }
}

export default new NavigationService();
