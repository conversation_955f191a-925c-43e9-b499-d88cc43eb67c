import React, { Suspense, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { NativeModules, Pressable, StyleSheet, Animated, Platform, LayoutChangeEvent, ScrollView } from 'react-native'
import Header from '../common/Headers/Header'
import { BackBtnPressListenerHelper } from '@xmly/rn-components'
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter'
import { RootState, store } from '../../store'
import { connect, useSelector } from 'react-redux'
import isEqual from 'lodash.isequal'
import { UserInfoContext } from '../../contextV2/userInfoContext'
import { useNavigation } from '@react-navigation/native'
import UserLoginStatus from '../../typesV2/userLoginStatus'
import goToLoginForAbTest from '../../utilsV2/goToLoginForAbTest'
import { ScrollAnalyticWapper, ScrollEventSender } from '@xmly/react-native-page-analytics'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { RouteMap } from '../../constantsV2/routeMap'
import monthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'
import { MonthlyTicketGuideStatus } from '../../typesV2/monthlyTicketGuide'
import ActivityWidget from '../ActivityWidget'
import { HomeScrollViewEventName, taskListAid } from '../../constantsV2'
import EntrancePromptConfirm from '../EntrancePromptConfirm'
import { TopSectionNotification } from '../../servicesV2/getTopSectionInfo'
import { CenterWrapper, HeaderBg, HeaderBgWrapper, ThemeBg } from './style'
import PropertyModule from '../PropertyModule'
import TaskCenter from 'componentsV2/EarnCredit/TaskCenter'
import TomorrowAward from '../TomorrowAward'
import { ScrollAreaContext } from '../../contextV2/scrollAreaContext'
import ADSection from '../ADSection'
import EverydayChallenge from '../EverydayChallenge'
import { ChannelUndertakeModalStatus } from '../../typesV2/channelModal'
import NewUserSignIn from '../NewUserSignIn'
import CashGuide from '../CashGuide'
import { PageScrollViewRefContext } from '../../contextV2/PageScrollViewRefContext'
import { HeaderContext } from '../../contextV2/HeaderContext'
import useQueryCashTask from 'hooks/useQueryCashTask'
import Push from 'components/Push'
import useCarveUpAward from 'components/CashTask/hook/useCarveUpAward'
import CarveUpSuccessModal from 'components/CashTask/CarveUpSuccessModal'
import TreasureBox from 'components/TreasureBox'
import CommodityList from 'components/Goods'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import useUpdateHitTraits from 'hooks/updateHitTraits'
import { useAtomValue } from 'jotai'
import { hitTraitsAtom } from 'components/CashTask/store/hitTraits'
import { withTheme } from 'styled-components'
import { RootNavigationProps, RootStackParamList } from '../../router/type'
import { tabsAtom } from 'atom/welfare'
import { usePageReport } from 'hooks/usePageReport'
import { safetyToString } from '@xmly/rn-utils'

const EverydayChallengeTaskListModalV2Lazy = React.lazy(() => import('../EverydayChallengeV2/TaskListModal'))
const GiftDescModalLazy = React.lazy(() => import('../GiftDescPopModal'))
const NotificationBarLazy = React.lazy(() => import('../NotificationBar'))
const ChannelUnderTakeModal = React.lazy(() => import('../ChannelUnderTakeModal'))
const CarveUpBanner = React.lazy(() => import('../../components/CashTask/CarveUpBanner'));
const CashModal = React.lazy(() => import('../../components/CashTask/CashModal'));
const GuideModal = React.lazy(() => import('../../components/CashTask/GuideModal'));

const MainTabListId = 'MainTabListId'

const viewStyle = { flex: 1 }

type Props = {
  creditPoint: number
  notification: TopSectionNotification[]
}

const ThemedHeaderBgWrapper = withTheme(HeaderBgWrapper)
const ThemedBg = withTheme(ThemeBg)

const Index: React.FC<Props> = (props) => {
  const { creditPoint, notification } = props;
  const navigation = useNavigation<RootNavigationProps>()
  const { loginStatus } = useContext(UserInfoContext)
  const nativeInfo = useContext(NativeInfoContext)
  const { guideStatus, hasShowMonthlyTicketGuide } = useContext(monthlyTicketGuideContext)
  const { modalStatus: channelModalStatus } = useSelector((state: RootState) => state.channelUndertake)
  const { noLoginAbTestStatus } = useSelector((state: RootState) => state.signInInfo)
  const { hasShowOneModalBefore } = useSelector((state: RootState) => state.signInInfo)
  const { taskList: everydayChallengeTaskList, summaryTask: everydayChallengeSummaryTask } = useSelector((state: RootState) => state.everydayChallenge)
  const scrollViewRef = useRef<ScrollView>(null)
  const shouldRefresh = useRef(false)
  const scrollValue = useRef(new Animated.Value(0)).current
  const [headerHeight, setHeaderHeight] = useState<number>();
  const [headerLabelIcon, setHeaderLabelIcon] = useState(false)
  const [showBigGiftDescModal, setShowBigGiftDescModal] = useState(false)
  const creditHeight = useRef(0)
  const safeAreaInsets = useSafeAreaInsets()
  const hideVip = useAtomValue(hitTraitsAtom);
  const { tabs } = useAtomValue(tabsAtom);
  const headerTabsProps = useMemo(() => {
    if (tabs.length > 0) {
      return {
        tabs: ['福利中心', '积分中心'],
        activeTab: '积分中心',
      }
    } else {
      return {}
    }
  }, [tabs])
  useQueryCashTask();
  useCarveUpAward();
  useUpdateHitTraits();
  usePageReport({
    pageViewCode: 30532,
    pageExitCode: 30533,
    currPage: '任务中心',
    params: {
      sourceType: safetyToString(nativeInfo?.channelName || nativeInfo?.srcChannel || ''),
    },
    otherProps: props
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady')
  }, [])

  const tryNavigationFromITing = useCallback(() => {
    // 这里只处理一次跳转，如果非登录状态不支持跳转
    if (loginStatus === UserLoginStatus.login) {
      const { route } = nativeInfo
      if (route && RouteMap.includes(route as keyof RootStackParamList)) {
        navigation.navigate(route as keyof RootStackParamList)
      }
    }
  }, [])

  useEffect(() => {
    tryNavigationFromITing()
  }, [])

  useEffect(() => {
    // 初次请求金币余额
    store.dispatch.goldCoin.getBalance()
    store.dispatch.chips.getChipsBalance()

    // iOS设备：如果是通过 route=drinkWater 进入的，立即自动跳转到喝水页面
    // 这里处理初始加载的情况（focus事件不会在初始加载时触发）
    if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
      console.log('MainTab: Auto navigating to DrinkWater for iOS route=drinkWater (initial load)');
      // 使用setTimeout确保页面完全加载后再跳转
      setTimeout(() => {
        navigation.navigate('DrinkWater');
      }, 100);
    }

    navigation.addListener('blur', () => {
      shouldRefresh.current = true
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    })

    navigation.addListener('focus', () => {
      if (shouldRefresh.current) {
        store.dispatch.credit.getCredit()
        store.dispatch.goldCoin.getBalance()
        store.dispatch.chips.getChipsBalance()
        store.dispatch.taskCenter.getTaskList({ aid: taskListAid })
        store.dispatch.everydayChallenge.getTaskList()
      }
      shouldRefresh.current = false
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }

      // iOS设备：如果是通过 route=drinkWater 进入的，自动跳转到喝水页面
      // 这里处理从其他页面返回到MainTab的情况
      if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater') {
        console.log('MainTab: Auto navigating to DrinkWater for iOS route=drinkWater (focus event)');
        // 使用setTimeout确保页面完全加载后再跳转
        setTimeout(() => {
          navigation.navigate('DrinkWater');
        }, 100);
      }
    })
  }, [])

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollValue } } }], {
    useNativeDriver: true,
    listener: (event: any) => {
      // CommodityCoverLazyControl.checker.schedule()
      GlobalEventEmitter.emit(HomeScrollViewEventName.onScroll)
      // 滚动时发送事件通知，会触发新出现的组件的曝光事件
      ScrollEventSender.send(MainTabListId, 'scroll')
      // listFooterInViewChecker.current.schedule()

      // 计算滑动距离
      // const height = headerHeight.current + barHeight.current + 11 + creditHeight.current
      const height = creditHeight.current

      // console.log("height:", height)
      // console.log("event.nativeEvent.contentOffset.y", Math.floor(event.nativeEvent.contentOffset.y))

      if (event.nativeEvent.contentOffset.y > height) {
        setHeaderLabelIcon(true)
      } else {
        setHeaderLabelIcon(false)
      }
    },
  })

  const handleBackButtonPress = () => {
    EntrancePromptConfirm(nativeInfo.channelName || nativeInfo.srcChannel || '')
      .then(() => {
        NativeModules.Page.finish(NativeModules.Page.OK, '')
      })
      .catch(() => { })
    return true
  }

  const handleMomentumScrollEnd = () => {
    GlobalEventEmitter.emit(HomeScrollViewEventName.onMomentumScrollEnd)
  }

  const TopSectionNotificationData = notification?.length ? notification[0] : null

  const ScrollAreaContextValue = useMemo(() => {
    return { scrollValue }
  }, [])

  const handleWrapperLayout = useCallback((event: LayoutChangeEvent) => {
    const width = event.nativeEvent.layout.width
    store.dispatch.page.setScreenWidth({ width })
  }, [])

  function onHeaderLayout(e: LayoutChangeEvent) {
    setHeaderHeight(e?.nativeEvent?.layout?.height)
  }

  const loginCondition = loginStatus === UserLoginStatus.notLogin && noLoginAbTestStatus === '2'

  return (
    <ScrollAreaContext.Provider value={ScrollAreaContextValue}>
      <ThemedBg onLayout={handleWrapperLayout}>
        <HeaderContext.Provider value={{ height: headerHeight }}>
          <Header
            label={headerLabelIcon ? (loginCondition ? '****' : creditPoint.toString()) : '签到领福利'}
            labelIcon={!!headerLabelIcon}
            withoutRight={headerLabelIcon}
            onLayout={onHeaderLayout}
            {...headerTabsProps}
            onTabPress={() => {
              navigation.replace('CoinCenter')
            }}
          />
          <ScrollAnalyticWapper
            id={MainTabListId}
            viewStyle={viewStyle}
            useNavigation={useNavigation}
          >
            <Animated.ScrollView
              onMomentumScrollEnd={handleMomentumScrollEnd}
              onMomentumScrollBegin={() => GlobalEventEmitter.emit(HomeScrollViewEventName.onMomentumScrollBegin)}
              onScrollBeginDrag={() => GlobalEventEmitter.emit(HomeScrollViewEventName.onScrollBeginDrag)}
              onScrollEndDrag={() => GlobalEventEmitter.emit(HomeScrollViewEventName.onScrollEndDrag)}
              onScroll={handleScroll}
              showsVerticalScrollIndicator={false}
              ref={scrollViewRef}
              style={{ paddingBottom: safeAreaInsets.bottom + 12 }}
            >
              <PageScrollViewRefContext.Provider value={scrollViewRef}>
                <CenterWrapper>
                  {TopSectionNotificationData && loginStatus === UserLoginStatus.login && guideStatus === MonthlyTicketGuideStatus.noNeed ? (
                    <Suspense fallback={null}>
                      <NotificationBarLazy />
                    </Suspense>
                  ) : null}

                  <PropertyModule
                    creditHeight={creditHeight}
                    handleShowBigGiftModal={() => {
                      setShowBigGiftDescModal(true)
                    }}
                  />
                  <NewUserSignIn />
                  <ADSection />
                  <Suspense fallback={null}>
                    <CarveUpBanner />
                  </Suspense>
                  <EverydayChallenge />
                  <TaskCenter />
                  <CommodityList />
                </CenterWrapper>
              </PageScrollViewRefContext.Provider>
            </Animated.ScrollView>
          </ScrollAnalyticWapper>
          <ThemedHeaderBgWrapper
            style={[
              {
                opacity: scrollValue.interpolate({
                  inputRange: [0, 300],
                  outputRange: [1, 0],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          >
            <HeaderBg resizeMode="cover" />
          </ThemedHeaderBgWrapper>
          {loginStatus !== UserLoginStatus.login && noLoginAbTestStatus !== '2' ? (
            <Pressable
              style={{
                ...StyleSheet.absoluteFillObject,
                width: '100%',
                height: '100%',
                marginTop: headerHeight,
              }}
              onPress={() => {
                goToLoginForAbTest()
              }}
            />
          ) : null}
        </HeaderContext.Provider>
      </ThemedBg>
      {/* 渠道承接弹窗 */}
      {channelModalStatus === ChannelUndertakeModalStatus.need ? (
        <Suspense fallback={null}>
          <ChannelUnderTakeModal />
        </Suspense>
      ) : null}

      {/* {!hasShowOneModalBefore && guideStatus === MonthlyTicketGuideStatus.need && channelModalStatus === ChannelUndertakeModalStatus.noNeed ? (
        <Suspense fallback={null}>
          <MonthlyTicketGuide />
        </Suspense>
      ) : null} */}

      <CashGuide />

      {showBigGiftDescModal && (
        <Suspense fallback={null}>
          <GiftDescModalLazy
            setClose={() => {
              setShowBigGiftDescModal(false)
            }}
          />
        </Suspense>
      )}

      <ActivityWidget />
      {/* 隔日领奖与月票引导弹窗互斥，未登录签到或未登录承接弹窗逻辑互斥 */}
      {!hasShowOneModalBefore && !hasShowMonthlyTicketGuide && channelModalStatus === ChannelUndertakeModalStatus.noNeed ? <TomorrowAward /> : null}

      {/* 任务包 - 任务列表面板 */}
      {!hideVip && everydayChallengeTaskList.length > 0 && everydayChallengeSummaryTask && everydayChallengeSummaryTask?.stepInfos && everydayChallengeSummaryTask?.stepInfos?.length > 0 ? (
        <Suspense fallback={null}>
          <EverydayChallengeTaskListModalV2Lazy />
        </Suspense>
      ) : null}
      <BackBtnPressListenerHelper
        withNavigation
        onBackBtnPress={handleBackButtonPress}
      />
      <Suspense fallback={null}>
        <CashModal />
        <GuideModal />
        <CarveUpSuccessModal />
      </Suspense>
      <Push />
      <TreasureBox />
    </ScrollAreaContext.Provider>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    creditPoint: state.credit.creditPoint,
    notification: state.topSection.global.notification,
  }
}
export default connect(mapStateToProps)(React.memo(Index, isEqual))
